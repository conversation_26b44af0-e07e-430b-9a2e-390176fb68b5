from sqlalchemy.orm import Session, joinedload
from sqlalchemy import select, or_
from typing import List, Optional, Dict, Any
from datetime import datetime, UTC

from db.models import (
    DraftExercise, DraftExerciseStatus, EditorAccount, EditorRole,
    DraftLearningNodeExercise,
    Chapter, LearningNode
)
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, ValidationError, PermissionDeniedError
)
from services.draft_management.scope_service import ScopeService
from api.v1.common.schemas import AppErrorCode
from loguru import logger

class DraftManagementService:
    @staticmethod
    async def get_editor_drafts(
        db: Session,
        editor: EditorAccount,
        status: Optional[DraftExerciseStatus] = None,
        subject_id: Optional[int] = None,
        chapter_id: Optional[int] = None,
        page: int = 1,
        page_size: int = 50
    ) -> Dict[str, Any]:
        """Get drafts visible to an editor based on their scope"""
        # Build base query with eager loading
        query = select(DraftExercise).options(
            joinedload(DraftExercise.learning_node_associations).joinedload(
                DraftLearningNodeExercise.learning_node
            ).joinedload(LearningNode.chapter).joinedload(Chapter.subject),
            joinedload(DraftExercise.assigned_editor),
            joinedload(DraftExercise.media_files)
        ).join(
            DraftLearningNodeExercise,
            DraftExercise.id == DraftLearningNodeExercise.draft_exercise_id
        ).join(
            LearningNode,
            DraftLearningNodeExercise.learning_node_id == LearningNode.id
        )
        
        # Apply status filter
        if status:
            query = query.where(DraftExercise.status == status)
        else:
            # By default, exclude drafts that don't need editor attention
            excluded_statuses = [
                DraftExerciseStatus.ACCEPTED_BY_EDITOR,  # Awaiting admin review
                DraftExerciseStatus.REJECTED_BY_EDITOR,  # Rejected drafts
                DraftExerciseStatus.PUBLISHED            # Already published
            ]
            query = query.where(~DraftExercise.status.in_(excluded_statuses))
        
        # Apply scope filters based on editor permissions
        scope_conditions = ScopeService.get_scope_filter_conditions(editor)
        if scope_conditions:
            query = query.where(or_(*scope_conditions))
        
        # Apply additional filters
        if subject_id:
            query = query.join(Chapter).where(Chapter.subject_id == subject_id)
        if chapter_id:
            query = query.where(LearningNode.chapter_id == chapter_id)
        
        # Order by updated_at descending
        query = query.order_by(DraftExercise.updated_at.desc())
        
        # Apply pagination
        offset = (page - 1) * page_size
        query = query.offset(offset).limit(page_size)
        
        # Execute query
        result = db.execute(query)
        drafts = result.unique().scalars().all()
        
        # Get total count
        count_query = select(DraftExercise).join(
            DraftLearningNodeExercise
        ).join(LearningNode)
        
        if status:
            count_query = count_query.where(DraftExercise.status == status)
        else:
            # Apply same exclusion logic for count
            excluded_statuses = [
                DraftExerciseStatus.ACCEPTED_BY_EDITOR,
                DraftExerciseStatus.REJECTED_BY_EDITOR,
                DraftExerciseStatus.PUBLISHED
            ]
            count_query = count_query.where(~DraftExercise.status.in_(excluded_statuses))
        if scope_conditions:
            count_query = count_query.where(or_(*scope_conditions))
        if subject_id:
            count_query = count_query.join(Chapter).where(Chapter.subject_id == subject_id)
        if chapter_id:
            count_query = count_query.where(LearningNode.chapter_id == chapter_id)
        
        # Execute the filtered count query
        from sqlalchemy import func
        count_stmt = select(func.count()).select_from(count_query.subquery())
        total_count = db.execute(count_stmt).scalar() or 0
        
        return {
            "drafts": list(drafts),
            "total": total_count,
            "page": page,
            "page_size": page_size,
            "total_pages": (total_count + page_size - 1) // page_size
        }
    
    @staticmethod
    async def get_draft_detail(
        db: Session,
        draft_id: int,
        editor: EditorAccount
    ) -> DraftExercise:
        """Get detailed information about a draft by database ID"""
        # Get draft with all relationships
        stmt = select(DraftExercise).options(
            joinedload(DraftExercise.learning_node_associations).joinedload(
                DraftLearningNodeExercise.learning_node
            ),
            joinedload(DraftExercise.assigned_editor),
            joinedload(DraftExercise.media_files),

        ).where(DraftExercise.id == draft_id)

        result = db.execute(stmt)
        draft = result.unique().scalar_one_or_none()

        if not draft:
            raise NotFoundError(
                message="Draft not found",
                entity_name="DraftExercise",
                identifier=draft_id,
                error_code=AppErrorCode.NOT_FOUND
            )

        # Check editor scope
        if not await ScopeService.check_editor_scope(db, editor, draft):
            raise PermissionDeniedError(
                message="Draft is outside your assigned scope",
                error_code=AppErrorCode.OUT_OF_SCOPE
            )

        return draft

    @staticmethod
    async def get_draft_detail_by_public_id(
        db: Session,
        draft_public_id: str,
        editor: EditorAccount
    ) -> DraftExercise:
        """Get detailed information about a draft by public ID"""
        # Get draft with all relationships
        stmt = select(DraftExercise).options(
            joinedload(DraftExercise.learning_node_associations).joinedload(
                DraftLearningNodeExercise.learning_node
            ),
            joinedload(DraftExercise.assigned_editor),
            joinedload(DraftExercise.media_files),

        ).where(DraftExercise.public_id == draft_public_id)

        result = db.execute(stmt)
        draft = result.unique().scalar_one_or_none()

        if not draft:
            raise NotFoundError(
                message="Draft not found",
                entity_name="DraftExercise",
                identifier=draft_public_id,
                error_code=AppErrorCode.NOT_FOUND
            )

        # Check editor scope
        if not await ScopeService.check_editor_scope(db, editor, draft):
            raise PermissionDeniedError(
                message="Draft is outside your assigned scope",
                error_code=AppErrorCode.OUT_OF_SCOPE
            )

        return draft
        
        # Check scope permissions
        if not await ScopeService.check_editor_scope(db, editor, draft):
            raise PermissionDeniedError(
                message="You don't have permission to view this draft",
                error_code=AppErrorCode.PERMISSION_DENIED
            )
        
        return draft
    
    @staticmethod
    async def claim_draft(
        db: Session,
        draft_id: int,
        editor: EditorAccount
    ) -> DraftExercise:
        """Claim a draft for editing (single-owner model)"""
        # Get draft
        draft = db.get(DraftExercise, draft_id)
        if not draft:
            raise NotFoundError(
                message="Draft not found",
                entity_name="DraftExercise",
                identifier=draft_id
            )
        
        # Check if draft is already assigned to another editor
        if draft.assigned_editor_id and draft.assigned_editor_id != editor.id:
            raise ValidationError(
                message="Draft is already assigned to another editor",
                error_code=AppErrorCode.INVALID_STATUS
            )
        
        # Check if draft can be claimed based on status
        if draft.status == DraftExerciseStatus.IN_REVIEW:
            raise ValidationError(
                message="Draft is already assigned to another editor",
                error_code=AppErrorCode.INVALID_STATUS
            )
        
        if draft.status not in [DraftExerciseStatus.NEW, DraftExerciseStatus.REJECTED_BY_ADMIN]:
            raise ValidationError(
                message=f"Cannot claim draft in status {draft.status.value}",
                error_code=AppErrorCode.INVALID_STATUS
            )
        
        # Check editor scope
        if not await ScopeService.check_editor_scope(db, editor, draft):
            raise PermissionDeniedError(
                message="Draft is outside your assigned scope"
            )
        
        # Claim the draft
        draft.assigned_editor_id = editor.id
        draft.status = DraftExerciseStatus.IN_REVIEW
        draft.updated_at = datetime.now(UTC)
        
        # Clear any previous rejection reason
        if draft.reject_reason:
            draft.reject_reason = None
        

        
        db.commit()
        logger.info(f"Draft {draft_id} claimed by editor {editor.email}")
        
        return draft
    
    @staticmethod
    async def update_draft(
        db: Session,
        draft_id: int,
        editor: EditorAccount,
        data: Dict[str, Any],
        solution: Optional[Dict[str, Any]] = None
    ) -> DraftExercise:
        """Update draft exercise data"""
        # Get draft
        draft = db.get(DraftExercise, draft_id)
        if not draft:
            raise NotFoundError(
                message="Draft not found",
                entity_name="DraftExercise",
                identifier=draft_id
            )
        
        # Check if draft was rejected
        if draft.status == DraftExerciseStatus.REJECTED_BY_EDITOR:
            raise ValidationError(
                message="Cannot update a rejected draft. This draft has been rejected and is awaiting admin review.",
                error_code=AppErrorCode.DRAFT_REJECTED
            )
        
        # Auto-claim NEW drafts if not assigned
        if draft.status == DraftExerciseStatus.NEW and not draft.assigned_editor_id:
            # Check editor scope before auto-claiming
            if not await ScopeService.check_editor_scope(db, editor, draft):
                raise PermissionDeniedError(
                    message="Draft is outside your assigned scope"
                )
            
            # Auto-claim the draft
            draft.assigned_editor_id = editor.id
            draft.status = DraftExerciseStatus.IN_REVIEW
            draft.updated_at = datetime.now(UTC)
            
            # Clear any previous rejection reason
            if draft.reject_reason:
                draft.reject_reason = None
            
            logger.info(f"Draft {draft_id} auto-claimed by editor {editor.email} during update")
        
        # Handle NEW drafts that are already assigned to the editor
        elif draft.status == DraftExerciseStatus.NEW and draft.assigned_editor_id == editor.id:
            # Transition to IN_REVIEW status
            draft.status = DraftExerciseStatus.IN_REVIEW
            draft.updated_at = datetime.now(UTC)
            logger.info(f"Draft {draft_id} transitioned from NEW to IN_REVIEW for editor {editor.email}")
        
        # Check ownership
        if draft.assigned_editor_id != editor.id and editor.role != EditorRole.ADMIN:
            raise PermissionDeniedError(
                message="You are not assigned to this draft",
                error_code=AppErrorCode.NOT_ASSIGNED
            )
        
        # Check status - allow editing for different statuses based on user role
        allowed_statuses = [DraftExerciseStatus.IN_REVIEW, DraftExerciseStatus.REJECTED_BY_ADMIN]

        # Admins can also edit drafts that are accepted by editors (for review purposes)
        if editor.role == EditorRole.ADMIN:
            allowed_statuses.append(DraftExerciseStatus.ACCEPTED_BY_EDITOR)

        if draft.status not in allowed_statuses:
            raise ValidationError(
                message=f"Cannot edit draft in status {draft.status.value}",
                error_code=AppErrorCode.INVALID_STATUS
            )
        
        # Update draft
        draft.data_json = data
        if solution is not None:
            draft.solution_json = solution
        draft.updated_at = datetime.now(UTC)
        
        # If previously rejected, change status back to IN_REVIEW
        if draft.status == DraftExerciseStatus.REJECTED_BY_ADMIN:
            draft.status = DraftExerciseStatus.IN_REVIEW
            draft.reject_reason = None
        

        db.commit()
        logger.info(f"Draft {draft_id} updated by editor {editor.email}")
        
        return draft

    @staticmethod
    async def update_draft_by_public_id(
        db: Session,
        draft_public_id: str,
        editor: EditorAccount,
        data: Dict[str, Any],
        solution: Optional[Dict[str, Any]] = None
    ) -> DraftExercise:
        """Update draft exercise data by public ID"""
        # Get draft by public ID
        draft = db.query(DraftExercise).filter(DraftExercise.public_id == draft_public_id).first()
        if not draft:
            raise NotFoundError(
                message="Draft not found",
                entity_name="DraftExercise",
                identifier=draft_public_id
            )

        # Check if draft was rejected
        if draft.status == DraftExerciseStatus.REJECTED_BY_EDITOR:
            raise ValidationError(
                message="Cannot update a rejected draft. This draft has been rejected and is awaiting admin review.",
                error_code=AppErrorCode.DRAFT_REJECTED
            )

        # Auto-claim NEW drafts if not assigned
        if draft.status == DraftExerciseStatus.NEW and not draft.assigned_editor_id:
            # Check editor scope before auto-claiming
            if not await ScopeService.check_editor_scope(db, editor, draft):
                raise PermissionDeniedError(
                    message="Draft is outside your assigned scope"
                )

            # Auto-claim the draft
            draft.assigned_editor_id = editor.id
            draft.status = DraftExerciseStatus.IN_REVIEW
            draft.updated_at = datetime.now(UTC)

            # Clear any previous rejection reason
            if draft.reject_reason:
                draft.reject_reason = None

            logger.info(f"Draft {draft_public_id} auto-claimed by editor {editor.email} during update")

        # Check ownership for non-admin editors
        if editor.role != EditorRole.ADMIN and draft.assigned_editor_id != editor.id:
            raise PermissionDeniedError(
                message="You are not assigned to this draft",
                error_code=AppErrorCode.NOT_ASSIGNED
            )

        # Check status - allow editing for different statuses based on user role
        allowed_statuses = [DraftExerciseStatus.IN_REVIEW, DraftExerciseStatus.REJECTED_BY_ADMIN]

        # Admins can also edit drafts that are accepted by editors (for review purposes)
        if editor.role == EditorRole.ADMIN:
            allowed_statuses.append(DraftExerciseStatus.ACCEPTED_BY_EDITOR)

        if draft.status not in allowed_statuses:
            raise ValidationError(
                message=f"Cannot edit draft in status {draft.status.value}",
                error_code=AppErrorCode.INVALID_STATUS
            )

        # Update draft
        draft.data_json = data
        if solution is not None:
            draft.solution_json = solution
        draft.updated_at = datetime.now(UTC)

        db.commit()
        db.refresh(draft)

        logger.info(f"Draft {draft_public_id} updated by editor {editor.email}")

        return draft

    @staticmethod
    async def accept_draft(
        db: Session,
        draft_id: int,
        editor: EditorAccount
    ) -> DraftExercise:
        """Accept a draft (mark as ready for admin review)"""
        # Get draft
        draft = db.get(DraftExercise, draft_id)
        if not draft:
            raise NotFoundError(
                message="Draft not found",
                entity_name="DraftExercise",
                identifier=draft_id
            )
        
        # Check ownership
        if draft.assigned_editor_id != editor.id:
            raise PermissionDeniedError(
                message="You are not assigned to this draft",
                error_code=AppErrorCode.NOT_ASSIGNED
            )
        
        # Check status
        if draft.status != DraftExerciseStatus.IN_REVIEW:
            raise ValidationError(
                message=f"Cannot accept draft in status {draft.status.value}",
                error_code=AppErrorCode.INVALID_STATUS
            )
        
        # Validate completeness
        if not draft.data_json or not draft.solution_json:
            raise ValidationError(
                message="Draft must have both exercise data and solution to be accepted"
            )
        
        if not draft.learning_node_associations:
            raise ValidationError(
                message="Draft must be associated with at least one learning node"
            )
        
        # Update status
        draft.status = DraftExerciseStatus.ACCEPTED_BY_EDITOR
        draft.updated_at = datetime.now(UTC)
        

        
        db.commit()
        logger.info(f"Draft {draft_id} accepted by editor {editor.email}")
        
        return draft

    @staticmethod
    async def accept_draft_by_public_id(
        db: Session,
        draft_public_id: str,
        editor: EditorAccount
    ) -> DraftExercise:
        """Accept a draft by public ID (mark as ready for admin review)"""
        # Get draft by public ID
        draft = db.query(DraftExercise).filter(DraftExercise.public_id == draft_public_id).first()
        if not draft:
            raise NotFoundError(
                message="Draft not found",
                entity_name="DraftExercise",
                identifier=draft_public_id
            )

        # Check ownership
        if draft.assigned_editor_id != editor.id:
            raise PermissionDeniedError(
                message="You are not assigned to this draft",
                error_code=AppErrorCode.NOT_ASSIGNED
            )

        # Check status
        if draft.status != DraftExerciseStatus.IN_REVIEW:
            raise ValidationError(
                message=f"Cannot accept draft in status {draft.status.value}",
                error_code=AppErrorCode.INVALID_STATUS
            )

        # Validate completeness
        if not draft.data_json or not draft.solution_json:
            raise ValidationError(
                message="Draft must have both exercise data and solution to be accepted"
            )

        if not draft.learning_node_associations:
            raise ValidationError(
                message="Draft must be associated with at least one learning node"
            )

        # Update status
        draft.status = DraftExerciseStatus.ACCEPTED_BY_EDITOR
        draft.updated_at = datetime.now(UTC)

        db.commit()
        logger.info(f"Draft {draft_public_id} accepted by editor {editor.email}")

        return draft

    @staticmethod
    async def create_draft(
        db: Session,
        editor: EditorAccount,
        exercise_type: str,
        difficulty: str,
        data_json: Dict[str, Any],
        solution_json: Dict[str, Any],
        learning_node_ids: List[str]
    ) -> DraftExercise:
        """Create a new draft exercise"""
        from db.models import LearningNode
        from db.models.draft_exercise import DraftLearningNodeExercise

        # Validate learning nodes exist and editor has access
        learning_nodes = []
        for node_public_id in learning_node_ids:
            node = db.query(LearningNode).filter(LearningNode.public_id == node_public_id).first()
            if not node:
                raise NotFoundError(
                    message=f"Learning node {node_public_id} not found",
                    entity_name="LearningNode",
                    identifier=node_public_id
                )
            learning_nodes.append(node)

        # Check editor scope for all learning nodes
        for node in learning_nodes:
            # Create a temporary draft to check scope
            temp_draft = DraftExercise(learning_node_associations=[
                DraftLearningNodeExercise(learning_node=node)
            ])
            if not await ScopeService.check_editor_scope(db, editor, temp_draft):
                raise PermissionDeniedError(
                    message=f"Learning node {node.title} is outside your assigned scope",
                    error_code=AppErrorCode.OUT_OF_SCOPE
                )

        # Create the draft
        draft = DraftExercise(
            exercise_type=exercise_type,
            difficulty=difficulty,
            data_json=data_json,
            solution_json=solution_json,
            status=DraftExerciseStatus.IN_REVIEW,  # Auto-assigned drafts go directly to IN_REVIEW
            assigned_editor_id=editor.id  # Auto-assign to creator
        )

        db.add(draft)
        db.flush()  # Get the draft ID

        # Create learning node associations
        for node in learning_nodes:
            association = DraftLearningNodeExercise(
                learning_node_id=node.id,
                draft_exercise_id=draft.id
            )
            db.add(association)



        db.commit()
        db.refresh(draft)

        logger.info(f"Draft {draft.id} created by editor {editor.email}")

        return draft

    @staticmethod
    async def reject_draft_by_admin(
        db: Session,
        draft_id: int,
        admin: EditorAccount,
        rejection_reason: str,
        suggested_changes: Optional[List[str]] = None
    ) -> DraftExercise:
        """
        Reject a draft by admin and send back to editor.
        
        Args:
            db: Database session
            draft_id: Draft to reject
            admin: Admin performing rejection
            rejection_reason: Detailed reason for rejection
            suggested_changes: Optional list of specific changes requested
            
        Returns:
            Updated draft exercise
        """
        # Get draft
        draft = db.query(DraftExercise).filter_by(id=draft_id).first()
        if not draft:
            raise NotFoundError(f"Draft {draft_id} not found")
        
        # Verify admin role
        if admin.role != EditorRole.ADMIN:
            raise PermissionDeniedError("Only admins can reject drafts")
        
        # Verify admin has scope
        if not await ScopeService.editor_has_scope_for_draft(db, admin, draft):
            raise PermissionDeniedError("Admin does not have scope for this draft")
        
        # Handle different statuses
        if draft.status == DraftExerciseStatus.ACCEPTED_BY_EDITOR:
            # Normal admin rejection flow - send back to editor
            draft.status = DraftExerciseStatus.REJECTED_BY_ADMIN
            draft.reject_reason = rejection_reason
        elif draft.status == DraftExerciseStatus.REJECTED_BY_EDITOR:
            # Admin overriding editor's rejection - reassign to the editor who rejected it
            # Find the editor who rejected it from metadata
            editor_email = None
            if draft.data_json and "metadata" in draft.data_json:
                editor_rejection = draft.data_json["metadata"].get("editor_rejection", {})
                editor_email = editor_rejection.get("rejected_by")
            
            # Try to find and reassign to the original editor
            if editor_email:
                original_editor = db.query(EditorAccount).filter_by(email=editor_email).first()
                if original_editor and original_editor.is_active:
                    draft.assigned_editor_id = original_editor.id
            
            # Change status to REJECTED_BY_ADMIN to indicate admin review
            draft.status = DraftExerciseStatus.REJECTED_BY_ADMIN
            draft.reject_reason = f"Admin review: {rejection_reason} (Previously rejected by editor)"
        else:
            raise ValidationError(
                f"Can only reject drafts in ACCEPTED_BY_EDITOR or REJECTED_BY_EDITOR status, current: {draft.status}"
            )
        
        # Store rejection metadata in data_json
        # Need to create a new dict to ensure SQLAlchemy detects the change
        data_json = draft.data_json or {}
        if "metadata" not in data_json:
            data_json["metadata"] = {}
        
        if suggested_changes:
            data_json["metadata"]["suggested_changes"] = suggested_changes
        data_json["metadata"]["rejection_timestamp"] = datetime.now(UTC).isoformat()
        data_json["metadata"]["rejected_by_admin"] = admin.email
        
        # Reassign to trigger SQLAlchemy change detection for JSON columns
        draft.data_json = data_json
        
        # Mark the JSON column as modified to ensure SQLAlchemy persists the change
        from sqlalchemy.orm.attributes import flag_modified
        flag_modified(draft, 'data_json')
        

        
        db.commit()
        db.refresh(draft)
        
        logger.info(f"Draft {draft_id} rejected by admin {admin.id}")

        return draft

    @staticmethod
    async def reject_draft_by_admin_public_id(
        db: Session,
        draft_public_id: str,
        admin: EditorAccount,
        rejection_reason: str,
        suggested_changes: Optional[List[str]] = None
    ) -> DraftExercise:
        """
        Reject a draft by admin and send back to editor using public ID.

        Args:
            db: Database session
            draft_public_id: Draft public ID to reject
            admin: Admin performing rejection
            rejection_reason: Detailed reason for rejection
            suggested_changes: Optional list of specific changes requested

        Returns:
            Updated draft exercise
        """
        # Get draft by public ID
        draft = db.query(DraftExercise).filter(DraftExercise.public_id == draft_public_id).first()
        if not draft:
            raise NotFoundError(f"Draft {draft_public_id} not found")

        # Verify admin role
        if admin.role != EditorRole.ADMIN:
            raise PermissionDeniedError("Only admins can reject drafts")

        # Verify admin has scope
        if not await ScopeService.editor_has_scope_for_draft(db, admin, draft):
            raise PermissionDeniedError("Admin does not have scope for this draft")

        # Handle different statuses
        if draft.status == DraftExerciseStatus.ACCEPTED_BY_EDITOR:
            # Normal admin rejection flow - send back to editor
            draft.status = DraftExerciseStatus.REJECTED_BY_ADMIN
            draft.reject_reason = rejection_reason
        elif draft.status == DraftExerciseStatus.REJECTED_BY_EDITOR:
            # Admin overriding editor's rejection - reassign to the editor who rejected it
            # Find the editor who rejected it from metadata
            editor_email = None
            if draft.data_json and "metadata" in draft.data_json:
                editor_rejection = draft.data_json["metadata"].get("editor_rejection", {})
                editor_email = editor_rejection.get("rejected_by")

            # Try to find and reassign to the original editor
            if editor_email:
                original_editor = db.query(EditorAccount).filter_by(email=editor_email).first()
                if original_editor and original_editor.is_active:
                    draft.assigned_editor_id = original_editor.id

            # Change status to REJECTED_BY_ADMIN to indicate admin review
            draft.status = DraftExerciseStatus.REJECTED_BY_ADMIN
            draft.reject_reason = f"Admin review: {rejection_reason} (Previously rejected by editor)"
        else:
            raise ValidationError(
                f"Can only reject drafts in ACCEPTED_BY_EDITOR or REJECTED_BY_EDITOR status, current: {draft.status}"
            )

        # Store rejection metadata in data_json
        # Need to create a new dict to ensure SQLAlchemy detects the change
        data_json = draft.data_json or {}
        if "metadata" not in data_json:
            data_json["metadata"] = {}

        if suggested_changes:
            data_json["metadata"]["suggested_changes"] = suggested_changes
        data_json["metadata"]["rejection_timestamp"] = datetime.now(UTC).isoformat()
        data_json["metadata"]["rejected_by_admin"] = admin.email

        # Reassign to trigger SQLAlchemy change detection for JSON columns
        draft.data_json = data_json

        # Mark the JSON column as modified to ensure SQLAlchemy persists the change
        from sqlalchemy.orm.attributes import flag_modified
        flag_modified(draft, 'data_json')

        db.commit()
        db.refresh(draft)

        logger.info(f"Draft {draft_public_id} rejected by admin {admin.id}")

        return draft

    @staticmethod
    async def reject_draft_by_editor(
        db: Session,
        draft_id: int,
        editor: EditorAccount,
        rejection_reason: str,
        rejection_type: Optional[str] = None
    ) -> DraftExercise:
        """
        Reject a draft by editor due to quality issues.
        
        Args:
            db: Database session
            draft_id: Draft to reject
            editor: Editor performing rejection
            rejection_reason: Detailed reason for rejection
            rejection_type: Optional categorization of rejection (quality/irrelevant/duplicate/other)
            
        Returns:
            Updated draft exercise
        """
        # Get draft
        draft = db.query(DraftExercise).filter_by(id=draft_id).first()
        if not draft:
            raise NotFoundError(f"Draft {draft_id} not found")
        
        # Verify editor has scope for this draft
        if not await ScopeService.editor_has_scope_for_draft(db, editor, draft):
            raise PermissionDeniedError("Editor does not have scope for this draft")
        
        # Check permission based on draft status
        if draft.status == DraftExerciseStatus.NEW:
            # NEW drafts can be rejected by any editor with scope
            pass
        elif draft.status == DraftExerciseStatus.IN_REVIEW:
            # IN_REVIEW drafts can only be rejected by the assigned editor
            if draft.assigned_editor_id != editor.id:
                raise PermissionDeniedError("You can only reject drafts assigned to you")
        elif draft.status == DraftExerciseStatus.REJECTED_BY_EDITOR:
            # Already rejected
            raise ValidationError("This draft has already been rejected")
        elif draft.status == DraftExerciseStatus.REJECTED_BY_ADMIN:
            # Rejected by admin
            raise ValidationError("This draft was rejected by an admin and needs to be revised")
        elif draft.status == DraftExerciseStatus.ACCEPTED_BY_EDITOR:
            # Already accepted
            raise ValidationError("This draft has already been accepted and is awaiting admin review")
        elif draft.status == DraftExerciseStatus.PUBLISHED:
            # Already published
            raise ValidationError("This draft has already been published")
        else:
            # Other statuses cannot be rejected
            raise ValidationError(f"Cannot reject drafts in {draft.status} status")
        
        # Update draft status and unassign
        draft.status = DraftExerciseStatus.REJECTED_BY_EDITOR
        draft.assigned_editor_id = None  # Unassign so it's not in editor's queue
        draft.reject_reason = rejection_reason
        
        # Store rejection metadata
        if not draft.data_json:
            draft.data_json = {}
        if "metadata" not in draft.data_json:
            draft.data_json["metadata"] = {}
        
        draft.data_json["metadata"]["editor_rejection"] = {
            "reason": rejection_reason,
            "type": rejection_type,
            "rejected_by": editor.email,
            "rejected_at": datetime.now(UTC).isoformat()
        }
        
        db.commit()
        
        logger.info(f"Draft {draft_id} rejected by editor {editor.id} ({editor.email})")
        
        return draft

    @staticmethod
    async def reject_draft_by_editor_public_id(
        db: Session,
        draft_public_id: str,
        editor: EditorAccount,
        rejection_reason: str,
        rejection_type: Optional[str] = None
    ) -> DraftExercise:
        """
        Reject a draft by editor due to quality issues using public ID.

        Args:
            db: Database session
            draft_public_id: Draft public ID to reject
            editor: Editor performing rejection
            rejection_reason: Detailed reason for rejection
            rejection_type: Optional categorization of rejection (quality/irrelevant/duplicate/other)

        Returns:
            Updated draft exercise
        """
        # Get draft by public ID
        draft = db.query(DraftExercise).filter(DraftExercise.public_id == draft_public_id).first()
        if not draft:
            raise NotFoundError(f"Draft {draft_public_id} not found")

        # Verify editor has scope for this draft
        if not await ScopeService.editor_has_scope_for_draft(db, editor, draft):
            raise PermissionDeniedError("Editor does not have scope for this draft")

        # Check permission based on draft status
        if draft.status == DraftExerciseStatus.NEW:
            # NEW drafts can be rejected by any editor with scope
            pass
        elif draft.status == DraftExerciseStatus.IN_REVIEW:
            # IN_REVIEW drafts can only be rejected by the assigned editor
            if draft.assigned_editor_id != editor.id:
                raise PermissionDeniedError("You can only reject drafts assigned to you")
        elif draft.status == DraftExerciseStatus.REJECTED_BY_EDITOR:
            # Already rejected
            raise ValidationError("This draft has already been rejected")
        elif draft.status == DraftExerciseStatus.REJECTED_BY_ADMIN:
            # Rejected by admin
            raise ValidationError("This draft was rejected by an admin and needs to be revised")
        elif draft.status == DraftExerciseStatus.ACCEPTED_BY_EDITOR:
            # Already accepted
            raise ValidationError("This draft has already been accepted and is awaiting admin review")
        elif draft.status == DraftExerciseStatus.PUBLISHED:
            # Already published
            raise ValidationError("This draft has already been published")
        else:
            # Other statuses cannot be rejected
            raise ValidationError(f"Cannot reject drafts in {draft.status} status")

        # Update draft status and unassign
        draft.status = DraftExerciseStatus.REJECTED_BY_EDITOR
        draft.assigned_editor_id = None  # Unassign so it's not in editor's queue
        draft.reject_reason = rejection_reason

        # Store rejection metadata
        if not draft.data_json:
            draft.data_json = {}
        if "metadata" not in draft.data_json:
            draft.data_json["metadata"] = {}

        draft.data_json["metadata"]["editor_rejection"] = {
            "reason": rejection_reason,
            "type": rejection_type,
            "rejected_by": editor.email,
            "rejected_at": datetime.now(UTC).isoformat()
        }

        db.commit()

        logger.info(f"Draft {draft_public_id} rejected by editor {editor.id} ({editor.email})")

        return draft

    @staticmethod
    async def delete_draft_by_admin(
        db: Session,
        draft_id: int,
        admin: EditorAccount
    ) -> bool:
        """
        Permanently delete a draft by admin.

        This is a hard delete operation that completely removes the draft
        and all associated data from the database.

        Args:
            db: Database session
            draft_id: Draft to delete
            admin: Admin performing deletion
            deletion_reason: Optional reason for deletion

        Returns:
            True if deletion was successful

        Raises:
            NotFoundError: If draft doesn't exist
            PermissionDeniedError: If admin lacks permission
            ValidationError: If draft cannot be deleted
        """
        # Get draft with all associations
        draft = db.query(DraftExercise).filter_by(id=draft_id).first()
        if not draft:
            raise NotFoundError(f"Draft {draft_id} not found")

        # Verify admin role
        if admin.role != EditorRole.ADMIN:
            raise PermissionDeniedError("Only admins can delete drafts")

        # Verify admin has scope
        if not await ScopeService.editor_has_scope_for_draft(db, admin, draft):
            raise PermissionDeniedError("Admin does not have scope for this draft")

        # Check if draft can be deleted (prevent deletion of published drafts)
        if draft.status == DraftExerciseStatus.PUBLISHED:
            raise ValidationError("Cannot delete published drafts")





        # Delete the draft (cascade will handle associations)
        # This includes:
        # - DraftLearningNodeExercise associations
        # - DraftMediaFile records
        # - Any other related data
        db.delete(draft)
        db.commit()

        logger.info(f"Draft {draft_id} permanently deleted by admin {admin.id}")

        return True