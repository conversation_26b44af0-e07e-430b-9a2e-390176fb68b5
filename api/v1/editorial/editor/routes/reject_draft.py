from fastapi import APIRouter, Depends, Path, HTTPException, status
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireEditor, EditorAuthResponse
)
from api.v1.editorial.editor.schemas.request import DraftRejectRequest
from api.v1.editorial.editor.schemas.response import DraftActionResponse
from services.draft_management.draft_service import DraftManagementService
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, PermissionDeniedError, ValidationError
)
from loguru import logger

router = APIRouter()

@router.post(
    "/drafts/{draft_public_id}/reject",
    response_model=DraftActionResponse,
    status_code=status.HTTP_200_OK,
    summary="Reject Draft",
    description="Reject a draft due to quality issues. The draft will be flagged for admin review."
)
async def reject_draft(
    draft_public_id: str = Path(..., description="Draft public ID to reject"),
    request: DraftRejectRequest = ...,
    auth: EditorAuthResponse = Depends(RequireEditor),
    db: Session = Depends(get_db)
):
    """
    Reject a draft due to quality issues.
    
    Requirements:
    - For NEW drafts: Any editor with scope can reject
    - For IN_REVIEW drafts: Only the assigned editor can reject
    - Rejection reason is required
    - Draft will be unassigned and marked as REJECTED_BY_EDITOR
    - Admins can then review and potentially delete the draft
    """
    try:
        # Reject the draft
        draft = await DraftManagementService.reject_draft_by_editor_public_id(
            db=db,
            draft_public_id=draft_public_id,
            editor=auth.editor,
            rejection_reason=request.rejection_reason,
            rejection_type=request.rejection_type
        )

        # Get updated draft details for response
        from .draft_detail import get_draft_detail
        detail_response = await get_draft_detail(draft_public_id, auth, db)
        
        return DraftActionResponse(
            success=True,
            message=f"Draft rejected successfully. Status: {draft.status.value}",
            draft=detail_response
        )
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.message
        )
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=e.message
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except Exception as e:
        logger.error(f"Error rejecting draft: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while rejecting the draft"
        )