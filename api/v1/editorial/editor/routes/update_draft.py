from fastapi import APIRouter, Depends, Path, HTTPException, status
from sqlalchemy.orm import Session
from db.database import get_db
from dependencies.auth_dependencies.editorial_auth_dependency import (
    RequireEditor, EditorAuthResponse
)
from api.v1.editorial.editor.schemas.request import DraftUpdateRequest
from api.v1.editorial.editor.schemas.response import DraftActionResponse
from services.draft_management.draft_service import DraftManagementService
from core.exception_handling.exceptions.custom_exceptions import (
    NotFoundError, PermissionDeniedError, ValidationError
)
from api.v1.common.schemas import AppErrorCode
from loguru import logger

router = APIRouter()

@router.patch(
    "/drafts/{draft_public_id}",
    response_model=DraftActionResponse,
    status_code=status.HTTP_200_OK,
    summary="Update Draft",
    description="Update draft exercise data and/or solution"
)
async def update_draft(
    draft_public_id: str = Path(..., description="Draft public ID"),
    request: DraftUpdateRequest = ...,
    auth: EditorAuthResponse = Depends(RequireEditor),
    db: Session = Depends(get_db)
):
    """
    Update a draft exercise.
    
    Only the assigned editor or an admin can update a draft.
    Draft must be in IN_REVIEW or REJECTED_BY_ADMIN status.
    
    Note: Rejected drafts (REJECTED_BY_EDITOR) cannot be updated.
    The frontend should stop auto-saving when a draft is rejected.
    """
    try:
        # Update the draft
        draft = await DraftManagementService.update_draft_by_public_id(
            db=db,
            draft_public_id=draft_public_id,
            editor=auth.editor,
            data=request.data_json,
            solution=request.solution_json
        )

        # Get updated draft details for response
        draft_detail = await DraftManagementService.get_draft_detail_by_public_id(
            db=db,
            draft_public_id=draft_public_id,
            editor=auth.editor
        )

        # Import the conversion logic from draft_detail.py
        from .draft_detail import get_draft_detail

        # Reuse the detail response conversion
        detail_response = await get_draft_detail(draft_public_id, auth, db)
        
        return DraftActionResponse(
            success=True,
            message="Draft updated successfully",
            draft=detail_response
        )
        
    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=e.message
        )
    except PermissionDeniedError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=e.message
        )
    except ValidationError as e:
        # Log validation errors at warning level (expected business logic)
        if hasattr(e, 'error_code') and e.error_code == AppErrorCode.DRAFT_REJECTED:
            logger.warning(f"Attempt to update rejected draft {draft_id}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=e.message
        )
    except Exception as e:
        # Only log unexpected errors
        if not (hasattr(e, 'error_code') and e.error_code in [AppErrorCode.NOT_ASSIGNED, AppErrorCode.INVALID_STATUS]):
            logger.error(f"Error updating draft: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An error occurred while updating the draft"
        )