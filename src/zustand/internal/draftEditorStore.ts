import { create } from 'zustand';
import { debounce } from 'lodash';
import { cloneDeep } from 'lodash';
import { getDraftById, getDraftByPublicId, updateDraft, updateDraftByPublicId, acceptDraft, acceptDraftByPublicId } from '@/services/internal/draftService';
import { DraftExercise } from '@/types/internal/editorial';
import { API_CONFIG, ERROR_MESSAGES, ANIMATION_CONFIG } from '@/config/constants';

interface DraftEditorState {
  draft: DraftExercise | null;
  originalDraft: DraftExercise | null; // For change detection
  isLoading: boolean;
  isSaving: boolean;
  isSubmitting: boolean;
  isDirty: boolean;
  error: string | null;
  lastSaved: string | null;
  autoSaveEnabled: boolean;
  autoSaveStatus: 'idle' | 'saving' | 'saved' | 'error';
}

interface DraftEditorActions {
  loadDraft: (draftPublicId: string) => Promise<void>;
  updateDraftData: (updates: Partial<DraftExercise>) => void;
  saveDraft: (showToast?: boolean) => Promise<boolean>;
  submitDraft: () => Promise<boolean>;
  enableAutoSave: () => void;
  disableAutoSave: () => void;
  reset: () => void;
  checkForUnsavedChanges: () => boolean;
  
  // Internal setters
  _setLoading: (isLoading: boolean) => void;
  _setSaving: (isSaving: boolean) => void;
  _setSubmitting: (isSubmitting: boolean) => void;
  _setError: (error: string | null) => void;
  _setDraft: (draft: DraftExercise | null) => void;
  _setDirty: (isDirty: boolean) => void;
}

export type DraftEditorStore = DraftEditorState & DraftEditorActions;

const initialState: DraftEditorState = {
  draft: null,
  originalDraft: null,
  isLoading: false,
  isSaving: false,
  isSubmitting: false,
  isDirty: false,
  error: null,
  lastSaved: null,
  autoSaveEnabled: true,
  autoSaveStatus: 'idle',
};

// Create debounced auto-save function outside the store
let debouncedAutoSave: ReturnType<typeof debounce> | null = null;

export const useDraftEditorStore = create<DraftEditorStore>((set, get) => {
  // Initialize debounced auto-save function
  if (!debouncedAutoSave) {
    debouncedAutoSave = debounce(async () => {
      const state = get();
      if (state.isDirty && state.autoSaveEnabled && !state.isSaving && state.draft) {
        try {
          set({ autoSaveStatus: 'saving' });
          await get().saveDraft(false); // Don't show toast for auto-save
          set({ autoSaveStatus: 'saved' });
          // Reset status after a short delay
          setTimeout(() => {
            if (get().autoSaveStatus === 'saved') {
              set({ autoSaveStatus: 'idle' });
            }
          }, ANIMATION_CONFIG.AUTO_SAVE_STATUS_DURATION);
        } catch (error) {
          set({ autoSaveStatus: 'error' });
          console.error('Auto-save failed:', error);
        }
      }
    }, API_CONFIG.AUTO_SAVE_DELAY);
  }

  return {
    ...initialState,

    loadDraft: async (draftPublicId: string) => {
      set({ isLoading: true, error: null });

      try {
        const result = await getDraftByPublicId(draftPublicId);

        if (result.status === 'success') {
          const draft = result.data;

          // Disable autosave for editor-rejected drafts since they can't be saved
          const isRejectedDraft = draft.status === 'REJECTED_BY_EDITOR';

          set({
            draft,
            originalDraft: cloneDeep(draft), // Proper deep clone
            isDirty: false,
            isLoading: false,
            autoSaveEnabled: !isRejectedDraft, // Disable autosave for rejected drafts
          });
        } else {
          throw new Error(result.message || ERROR_MESSAGES.DRAFT_LOAD_FAILED);
        }
      } catch (error) {
        console.error('Failed to load draft:', error);
        set({
          error: error instanceof Error ? error.message : ERROR_MESSAGES.DRAFT_LOAD_FAILED,
          isLoading: false,
        });
      }
    },

    updateDraftData: (updates) => {
      const currentDraft = get().draft;
      if (!currentDraft) return;
      
      // Prevent updating drafts rejected by editor (but allow admin-rejected drafts)
      if (currentDraft.status === 'REJECTED_BY_EDITOR') {
        console.warn('Cannot update draft rejected by editor');
        return;
      }

      const updatedDraft = { ...currentDraft, ...updates };
      set({
        draft: updatedDraft,
        isDirty: true,
        autoSaveStatus: 'idle', // Reset auto-save status when user makes changes
      });

      // Trigger debounced auto-save if enabled
      if (get().autoSaveEnabled && !get().isSaving && debouncedAutoSave) {
        debouncedAutoSave();
      }
    },

    saveDraft: async (showToast = true) => {
      const { draft, isDirty, isSaving, originalDraft } = get();
      
      if (!draft || !isDirty || isSaving) return false;
      
      // Prevent saving drafts rejected by editor (but allow admin-rejected drafts)
      if (draft.status === 'REJECTED_BY_EDITOR') {
        console.warn('Cannot save draft rejected by editor');
        return false;
      }

      // Store previous state for potential rollback
      const previousDraft = cloneDeep(draft);
      
      set({ isSaving: true, error: null });

      try {
        const updates = {
          dataJson: draft.dataJson,
          solutionJson: draft.solutionJson,
          difficulty: draft.difficulty,
        };

        const result = await updateDraftByPublicId(draft.publicId, updates);

        if (result.status === 'success') {
          const updatedDraft = result.data.draft;
          set({
            draft: updatedDraft,
            originalDraft: cloneDeep(updatedDraft), // Proper deep clone
            isDirty: false,
            lastSaved: new Date().toISOString(),
            isSaving: false,
          });

          return true;
        } else {
          throw new Error(result.message || ERROR_MESSAGES.DRAFT_SAVE_FAILED);
        }
      } catch (error) {
        console.error('Failed to save draft:', error);
        
        // Rollback on failure - restore previous state
        set({
          draft: previousDraft,
          error: error instanceof Error ? error.message : ERROR_MESSAGES.DRAFT_SAVE_FAILED,
          isSaving: false,
          autoSaveStatus: 'error',
        });
        
        return false;
      }
    },

  submitDraft: async () => {
    const { draft, isSaving, isSubmitting } = get();
    
    if (!draft || isSaving || isSubmitting) return false;

    set({ isSubmitting: true, error: null });

    try {
      // Save first if there are unsaved changes
      if (get().isDirty) {
        const saveSuccess = await get().saveDraft(false);
        if (!saveSuccess) {
          set({ isSubmitting: false });
          return false;
        }
      }

      const result = await acceptDraftByPublicId(draft.publicId);

      if (result.status === 'success') {
        // Update draft status
        set({
          draft: { ...draft, status: 'ACCEPTED_BY_EDITOR' },
          isDirty: false,
          isSubmitting: false,
        });

        return true;
      } else {
        throw new Error(result.message || ERROR_MESSAGES.DRAFT_SUBMIT_FAILED);
      }
    } catch (error) {
      console.error('Failed to submit draft:', error);
      set({
        error: error instanceof Error ? error.message : ERROR_MESSAGES.DRAFT_SUBMIT_FAILED,
        isSubmitting: false,
      });
      return false;
    }
  },



  enableAutoSave: () => set({ autoSaveEnabled: true }),
  disableAutoSave: () => set({ autoSaveEnabled: false }),

  checkForUnsavedChanges: () => {
    return get().isDirty;
  },

    reset: () => {
      // Cancel any pending auto-save
      if (debouncedAutoSave) {
        debouncedAutoSave.cancel();
      }
      set(initialState);
    },

  // Internal setters
  _setLoading: (isLoading) => set({ isLoading }),
  _setSaving: (isSaving) => set({ isSaving }),
  _setSubmitting: (isSubmitting) => set({ isSubmitting }),
    _setError: (error) => set({ error }),
    _setDraft: (draft) => set({ draft }),
    _setDirty: (isDirty) => set({ isDirty }),
  };
});

// Selector helpers
export const selectDraft = (state: DraftEditorStore) => state.draft;
export const selectIsLoading = (state: DraftEditorStore) => state.isLoading;
export const selectIsSaving = (state: DraftEditorStore) => state.isSaving;
export const selectIsSubmitting = (state: DraftEditorStore) => state.isSubmitting;
export const selectIsDirty = (state: DraftEditorStore) => state.isDirty;
export const selectError = (state: DraftEditorStore) => state.error;
export const selectCanSubmit = (state: DraftEditorStore) => {
  return state.draft && 
         (state.draft.status === 'NEW' || state.draft.status === 'IN_REVIEW' || state.draft.status === 'REJECTED_BY_ADMIN') && 
         !state.isSubmitting && 
         !state.isLoading;
};
export const selectAutoSaveStatus = (state: DraftEditorStore) => state.autoSaveStatus;