import { ServiceResult } from '@/types/services/serviceResultType';
import { AUTH_HEADERS } from '@/config/constants';
import { useInternalAuthStore } from '@/zustand/internal/internalAuthStore/internalAuthStore';
import {
  CurrentEditorSchema,
  CurrentEditorApp,
  EditorLearningNodesResponseSchema,
  EditorLearningNodesResponseApp,
  LearningNodeDetailSchema,
  LearningNodeDetailApp,
  LearningNodeContentSchema,
  LearningNodeContentApp
} from '@/schemas/internal/editorSchema';
import {
  RejectDraftRequestSchema,
  RejectDraftResponseSchema,
  RejectDraftRequestApp,
  RejectDraftResponseApp
} from '@/schemas/internal/adminSchema';
import { z } from 'zod';
import { customFetchInternal } from '../serviceUtils';

const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL;
const API_URL = `${BACKEND_URL}/api/v1`;
const EDITORIAL_BASE_URL = `${API_URL}/editorial`;
const EDITOR_BASE_URL = `${EDITORIAL_BASE_URL}/editor`;

/**
 * Get current editor's profile including assigned scopes
 */
export const getCurrentEditor = (): Promise<ServiceResult<CurrentEditorApp>> => {
  return customFetchInternal(`${EDITOR_BASE_URL}/me`, {
    method: 'GET',
    responseSchemaPair: CurrentEditorSchema,
  });
};

/**
 * Get learning nodes assigned to the current editor
 * Uses the /editor/learning-nodes endpoint which returns nodes based on editor's scopes
 */
export const getAssignedLearningNodes = async (): Promise<ServiceResult<EditorLearningNodesResponseApp>> => {
  return customFetchInternal(`${EDITOR_BASE_URL}/learning-nodes`, {
    method: 'GET',
    responseSchemaPair: EditorLearningNodesResponseSchema,
  });
};

/**
 * Get detailed information about a specific learning node
 * Only returns nodes that the editor has access to based on their scopes
 */
export const getLearningNodeDetail = async (learningNodeId: string): Promise<ServiceResult<LearningNodeDetailApp>> => {
  return customFetchInternal(`${EDITOR_BASE_URL}/learning-nodes/${learningNodeId}`, {
    method: 'GET',
    responseSchemaPair: LearningNodeDetailSchema,
  });
};

/**
 * Get full learning node content including notes for editors
 * Uses the editor-specific endpoint with proper editor authentication
 * Only works for learning nodes that the editor has access to
 */
export const getLearningNodeContent = async (learningNodeId: string): Promise<ServiceResult<LearningNodeContentApp>> => {
  return customFetchInternal(`${EDITOR_BASE_URL}/learning-nodes/${learningNodeId}/content`, {
    method: 'GET',
    responseSchemaPair: LearningNodeContentSchema,
  });
};

/**
 * Reject a draft as an editor with feedback (legacy - for backward compatibility)
 * This allows editors to reject drafts they are reviewing
 */
export const rejectDraftAsEditor = (
  draftId: number,
  params: RejectDraftRequestApp
): Promise<ServiceResult<RejectDraftResponseApp>> => {
  return customFetchInternal(`${EDITOR_BASE_URL}/drafts/${draftId}/reject`, {
    method: 'POST',
    body: params,
    requestSchemaPair: RejectDraftRequestSchema,
    responseSchemaPair: RejectDraftResponseSchema,
  });
};

/**
 * Reject a draft as an editor with feedback using public ID
 * This allows editors to reject drafts they are reviewing
 */
export const rejectDraftAsEditorByPublicId = (
  draftPublicId: string,
  params: RejectDraftRequestApp
): Promise<ServiceResult<RejectDraftResponseApp>> => {
  return customFetchInternal(`${EDITOR_BASE_URL}/drafts/${draftPublicId}/reject`, {
    method: 'POST',
    body: params,
    requestSchemaPair: RejectDraftRequestSchema,
    responseSchemaPair: RejectDraftResponseSchema,
  });
};