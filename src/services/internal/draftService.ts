import { ServiceResult } from '@/types/services/serviceResultType';
import { AUTH_HEADERS } from '@/config/constants';
import { 
  GetDraftsRequestSchema,
  GetDraftsResponseSchema,
  GetDraftDetailResponseSchema,
  UpdateDraftRequestSchema,
  UpdateDraftResponseSchema,
  AcceptDraftRequestSchema,
  AcceptDraftResponseSchema,
  CreateDraftRequestSchema,
  CreateDraftResponseSchema,
  GetDraftsRequestApp,
  GetDraftsResponseApp,
  GetDraftDetailResponseApp,
  UpdateDraftRequestApp,
  UpdateDraftResponseApp,
  AcceptDraftRequestApp,
  AcceptDraftResponseApp,
  CreateDraftRequestApp,
  CreateDraftResponseApp
} from '@/schemas/internal/draftSchema';
import { customFetchInternal } from '../serviceUtils';

const BACKEND_URL = process.env.NEXT_PUBLIC_BACKEND_URL;
const API_URL = `${BACKEND_URL}/api/v1`;
const EDITORIAL_BASE_URL = `${API_URL}/editorial`;
const EDITOR_BASE_URL = `${EDITORIAL_BASE_URL}/editor`;

/**
 * Get drafts with optional filters and pagination
 */
export const getDrafts = (params: GetDraftsRequestApp): Promise<ServiceResult<GetDraftsResponseApp>> => {
  // Transform parameters using the request schema to convert to snake_case
  const apiParams = GetDraftsRequestSchema.toApi(params);
  
  const queryParams = new URLSearchParams();
  
  // Add query parameters
  Object.entries(apiParams).forEach(([key, value]) => {
    if (value !== undefined && value !== null) {
      queryParams.append(key, String(value));
    }
  });

  const url = `${EDITOR_BASE_URL}/drafts${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
  
  return customFetchInternal(url, {
    method: "GET",
    responseSchemaPair: GetDraftsResponseSchema,
  });
};

/**
 * Get draft details by ID (legacy - for backward compatibility)
 */
export const getDraftById = (draftId: number): Promise<ServiceResult<GetDraftDetailResponseApp>> => {
  return customFetchInternal(`${EDITOR_BASE_URL}/drafts/${draftId}`, {
    method: "GET",
    responseSchemaPair: GetDraftDetailResponseSchema,
  });
};

/**
 * Get draft details by public ID
 */
export const getDraftByPublicId = (draftPublicId: string): Promise<ServiceResult<GetDraftDetailResponseApp>> => {
  return customFetchInternal(`${EDITOR_BASE_URL}/drafts/${draftPublicId}`, {
    method: "GET",
    responseSchemaPair: GetDraftDetailResponseSchema,
  });
};

/**
 * Update a draft (legacy - for backward compatibility)
 */
export const updateDraft = (
  draftId: number,
  params: UpdateDraftRequestApp
): Promise<ServiceResult<UpdateDraftResponseApp>> => {
  return customFetchInternal(`${EDITOR_BASE_URL}/drafts/${draftId}`, {
    method: "PATCH",
    body: params,
    requestSchemaPair: UpdateDraftRequestSchema,
    responseSchemaPair: UpdateDraftResponseSchema,
  });
};

/**
 * Update a draft by public ID
 */
export const updateDraftByPublicId = (
  draftPublicId: string,
  params: UpdateDraftRequestApp
): Promise<ServiceResult<UpdateDraftResponseApp>> => {
  return customFetchInternal(`${EDITOR_BASE_URL}/drafts/${draftPublicId}`, {
    method: "PATCH",
    body: params,
    requestSchemaPair: UpdateDraftRequestSchema,
    responseSchemaPair: UpdateDraftResponseSchema,
  });
};

/**
 * Accept a draft (submit for admin review) - legacy for backward compatibility
 */
export const acceptDraft = (
  draftId: number,
  params: AcceptDraftRequestApp = {}
): Promise<ServiceResult<AcceptDraftResponseApp>> => {
  return customFetchInternal(`${EDITOR_BASE_URL}/drafts/${draftId}/accept`, {
    method: "POST",
    body: params,
    requestSchemaPair: AcceptDraftRequestSchema,
    responseSchemaPair: AcceptDraftResponseSchema,
  });
};

/**
 * Accept a draft by public ID (submit for admin review)
 */
export const acceptDraftByPublicId = (
  draftPublicId: string,
  params: AcceptDraftRequestApp = {}
): Promise<ServiceResult<AcceptDraftResponseApp>> => {
  return customFetchInternal(`${EDITOR_BASE_URL}/drafts/${draftPublicId}/accept`, {
    method: "POST",
    body: params,
    requestSchemaPair: AcceptDraftRequestSchema,
    responseSchemaPair: AcceptDraftResponseSchema,
  });
};

/**
 * Create a new draft
 */
export const createDraft = (params: CreateDraftRequestApp): Promise<ServiceResult<CreateDraftResponseApp>> => {
  return customFetchInternal(`${EDITOR_BASE_URL}/drafts`, {
    method: "POST",
    body: params,
    requestSchemaPair: CreateDraftRequestSchema,
    responseSchemaPair: CreateDraftResponseSchema,
  });
};

/**
 * Get drafts for a specific learning node
 */
export const getDraftsForLearningNode = (learningNodePublicId: string): Promise<ServiceResult<GetDraftsResponseApp>> => {
  // Note: The backend expects learning_node_id as a number (internal ID),
  // but we only have the public ID. For now, we'll filter by learning node public ID
  // in the frontend after fetching all drafts. This should be optimized in the backend later.
  return getDrafts({ page: 1, pageSize: 50 });
};