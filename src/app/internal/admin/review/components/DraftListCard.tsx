import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CollapsibleSkeleton, EmptyState } from '@/components/internal/LoadingStates';
import { CheckSquare } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { DraftListCardProps } from '../types';
import { DraftRow } from './DraftRow';
import { ChapterSection } from './ChapterSection';
import { SubjectSection } from './SubjectSection';
import { UI_TEXT } from '../constants';
import { getDraftLearningNodeId } from '../utils/draftHelpers';
import { DraftListItem } from '@/types/internal/editorial';

export function DraftListCard({
  groupedDrafts,
  selectedDraftIds,
  expandedSubjects,
  isLoading,
  onToggleSubject,
  onSelectDraft,
  onSelectAllInChapter,
  onSelectAll,
  onDeselectAll,
  onPublishDraft,
  onRejectDraft,
  onDeleteDraft,
  onReviewDraft,
  isPublishing,
  isRejecting,
  isDeleting,
}: DraftListCardProps) {
  const router = useRouter();
  const selectedCount = selectedDraftIds.length;

  const handleReviewDraft = (draft: DraftListItem) => {
    const learningNodeId = getDraftLearningNodeId(draft);
    if (learningNodeId) {
      router.push(`/internal/editor/${learningNodeId}/${draft.publicId}`);
    } else {
      console.error('Draft has no associated learning nodes:', draft.publicId);
    }
  };

  const renderDraftRow = (draft: DraftListItem) => (
    <DraftRow
      key={draft.id}
      draft={draft}
      isSelected={selectedDraftIds.includes(draft.id)}
      onSelect={(isSelected) => onSelectDraft(draft.id, isSelected)}
      onReview={() => handleReviewDraft(draft)}
      onPublish={() => onPublishDraft(draft.publicId)}
      onReject={() => onRejectDraft(draft.id)}
      onDelete={() => onDeleteDraft(draft)}
      isPublishing={isPublishing}
      isRejecting={isRejecting}
      isDeleting={isDeleting}
    />
  );

  const renderChapter = (subjectName: string, chapterTitle: string, drafts: DraftListItem[]) => (
    <ChapterSection
      key={chapterTitle}
      subjectName={subjectName}
      chapterTitle={chapterTitle}
      drafts={drafts}
      selectedDraftIds={selectedDraftIds}
      onSelectAll={(isSelected) => onSelectAllInChapter(subjectName, chapterTitle, isSelected)}
      renderDraftRow={renderDraftRow}
    />
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Drafts by Subject & Chapter</span>
          <div className="flex items-center gap-2">
            {selectedCount > 0 && (
              <span className="text-sm text-gray-600 mr-2">{selectedCount} selected</span>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => onSelectAll()}
            >
              Select All
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onDeselectAll}
            >
              Deselect All
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <CollapsibleSkeleton sections={3} />
        ) : Object.keys(groupedDrafts).length > 0 ? (
          <div className="space-y-4">
            {Object.entries(groupedDrafts).map(([subjectName, chapters]) => (
              <SubjectSection
                key={subjectName}
                subjectName={subjectName}
                chapters={chapters}
                isExpanded={expandedSubjects.includes(subjectName)}
                selectedDraftIds={selectedDraftIds}
                onToggle={() => onToggleSubject(subjectName)}
                renderChapter={renderChapter}
              />
            ))}
          </div>
        ) : (
          <EmptyState
            title={UI_TEXT.emptyState.title}
            message={UI_TEXT.emptyState.message}
            icon={<CheckSquare className="h-12 w-12 mx-auto" />}
          />
        )}
      </CardContent>
    </Card>
  );
}