'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';
import { createDraft as createDraftService } from '@/services/internal/draftService';
import { ExerciseTemplate, getExerciseTypesByCategory } from '@/utils/internal/exerciseTemplates';
import { Difficulty } from '@/types/internal/editorial';
import { UseNewDraftReturn } from '../types';

export const useNewDraft = (learningNodeId: string): UseNewDraftReturn => {
  const router = useRouter();
  const { toast } = useToast();
  
  // State
  const [step, setStep] = useState<'template' | 'settings'>('template');
  const [selectedTemplate, setSelectedTemplate] = useState<ExerciseTemplate | null>(null);
  const [difficulty, setDifficulty] = useState<Difficulty>('medium');
  const [isCreating, setIsCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Actions
  const selectTemplate = (template: ExerciseTemplate) => {
    setSelectedTemplate(template);
    setStep('settings');
  };

  const navigateBack = () => {
    if (step === 'settings') {
      setStep('template');
      setSelectedTemplate(null);
    } else {
      router.push(`/internal/editor/${learningNodeId}`);
    }
  };

  const createDraft = async () => {
    if (!selectedTemplate || !learningNodeId) {
      toast({
        title: 'Missing information',
        description: 'Unable to create draft. Missing template or learning node.',
        variant: 'destructive',
      });
      return;
    }

    setIsCreating(true);
    setError(null);

    try {
      const result = await createDraftService({
        exerciseType: selectedTemplate.type,
        difficulty,
        dataJson: selectedTemplate.dataTemplate,
        solutionJson: selectedTemplate.solutionTemplate,
        learningNodeIds: [learningNodeId],
      });

      if (result.status === 'success') {
        toast({
          title: 'Draft created',
          description: 'Your new draft has been created successfully.',
        });
        router.push(`/internal/editor/${learningNodeId}/${result.data.draft.publicId}`);
      } else {
        throw new Error(result.message || 'Failed to create draft');
      }
    } catch (error) {
      console.error('Failed to create draft:', error);
      const errorMessage = error instanceof Error ? error.message : 'Failed to create draft';
      setError(errorMessage);
      toast({
        title: 'Creation failed',
        description: errorMessage,
        variant: 'destructive',
      });
    } finally {
      setIsCreating(false);
    }
  };

  const resetError = () => setError(null);

  return {
    // State
    step,
    selectedTemplate,
    difficulty,
    isCreating,
    error,
    categories: getExerciseTypesByCategory(),
    
    // Actions
    selectTemplate,
    setDifficulty,
    navigateBack,
    createDraft,
    resetError,
  };
};