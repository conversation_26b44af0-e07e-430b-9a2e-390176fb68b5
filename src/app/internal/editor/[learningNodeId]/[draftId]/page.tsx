'use client';

import { useParams, useRouter } from 'next/navigation';
import { useDraftEditorPage } from './hooks/useDraftEditorPage';
import { LoadingState } from './components/LoadingState';
import { ErrorState } from './components/ErrorState';
import { DraftEditorHeader } from './components/DraftEditorHeader';
import { StepIndicator } from './components/StepIndicator';
import { StepNavigation } from './components/StepNavigation';
import { SubmitDialog } from './components/SubmitDialog';
import { RejectDraftModal } from './components/RejectDraftModal';
import { ExerciseStep } from './components/steps/ExerciseStep';
import { SolutionStep } from './components/steps/SolutionStep';
import { ReviewStep } from './components/steps/ReviewStep';

export default function DraftEditorPage() {
  const params = useParams();
  const router = useRouter();
  const draftPublicId = params.draftId as string;
  const learningNodePublicId = params.learningNodeId as string;
  
  const {
    draft,
    isLoading,
    isSaving,
    isSubmitting,
    isDirty,
    error,
    canSubmit,
    canReject,
    autoSaveStatus,
    save,
    updateExerciseData,
    updateSolutionData,
    updateDifficulty,
    showRejectModal,
    setShowRejectModal,
    rejectReason,
    setRejectReason,
    isRejecting,
    handleReject,
    stepNavigation,
    submitDialog,
    solutionEditor,
  } = useDraftEditorPage(draftPublicId, learningNodePublicId);

  const { currentStep, canProceedToNextStep, handleNextStep, handlePreviousStep } = stepNavigation;
  const { showSubmitDialog, setShowSubmitDialog, handleSubmit } = submitDialog;
  const { solutionEditorMode, setSolutionEditorMode } = solutionEditor;

  if (isLoading) {
    return <LoadingState />;
  }

  if (error || !draft) {
    return (
      <ErrorState
        error={error}
        learningNodeId={learningNodePublicId}
        onBack={() => router.push(`/internal/editor/${learningNodePublicId}`)}
      />
    );
  }

  return (
    <div className="h-full flex flex-col w-full max-w-[1500px] mx-auto">
      {/* Header */}
      <div className="flex-none space-y-4 px-6 pt-6">
        <DraftEditorHeader
          draft={draft}
          isDirty={isDirty}
          isSaving={isSaving}
          canSubmit={canSubmit}
          isSubmitting={isSubmitting}
          canReject={canReject}
          autoSaveStatus={autoSaveStatus}
          onSave={save}
          onSubmit={() => setShowSubmitDialog(true)}
          onReject={() => setShowRejectModal(true)}
          onBack={() => router.back()}
        />
        
        {/* Step Progress Indicator */}
        <div className="bg-white border rounded-lg p-4">
          <StepIndicator currentStep={currentStep} />
          
          <StepNavigation
            currentStep={currentStep}
            canProceedToNextStep={canProceedToNextStep}
            onNextStep={handleNextStep}
            onPreviousStep={handlePreviousStep}
            canSubmit={canSubmit}
            isDirty={isDirty}
            isSubmitting={isSubmitting}
            onSubmit={() => setShowSubmitDialog(true)}
          />
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 min-h-0 overflow-y-auto px-6 pb-6 mt-4">
        {currentStep === 1 && (
          <ExerciseStep draft={draft} updateExerciseData={updateExerciseData} />
        )}
        
        {currentStep === 2 && (
          <SolutionStep
            draft={draft}
            updateSolutionData={updateSolutionData}
            solutionEditorMode={solutionEditorMode}
            onEditorModeChange={setSolutionEditorMode}
          />
        )}
        
        {currentStep === 3 && (
          <ReviewStep
            draft={draft}
            canSubmit={canSubmit}
            isDirty={isDirty}
            isSubmitting={isSubmitting}
            onSubmit={() => setShowSubmitDialog(true)}
            updateDifficulty={updateDifficulty}
          />
        )}
      </div>

      <SubmitDialog
        isOpen={showSubmitDialog}
        isSubmitting={isSubmitting}
        onClose={() => setShowSubmitDialog(false)}
        onSubmit={handleSubmit}
      />
      
      <RejectDraftModal
        isOpen={showRejectModal}
        reason={rejectReason}
        isRejecting={isRejecting}
        onReasonChange={setRejectReason}
        onConfirm={handleReject}
        onClose={() => setShowRejectModal(false)}
      />
    </div>
  );
}