'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { CategorizeExercise as CategorizeExerciseType, ExerciseSubmissionResponse } from '../exerciseTypes';
import SubmissionButton from '../components/SubmissionButton';
import { useExercises } from '../useExercises';
import { ExerciseSolution } from "../components/ExerciseSolution";
import SubmissionAnimation from '../components/SubmissionAnimation';
import SubmissionError from '../components/SubmissionError';
import CelebrationEffect from '../components/CelebrationEffect';
import IncorrectAnswerEffect from '../components/IncorrectAnswerEffect';
import { Language } from '@/i18n/types';
import { useLanguage } from '@/hooks/useLanguage';
import LatexRenderer from '@/components/shared/LatexRenderer';

const languageData = {
  en: {
    instructions: "Categorize items by clicking an item on the left, then clicking its category on the right.",
    instructionsMobile: "Tap an item, then tap its category.",
    categories: "Categories",
    items: "Items to Categorize",
    removeAssignment: "Remove assignment",
    selectItem: "Select an item first",
    itemSelected: "Item selected",
    allItemsCategorized: "All items have been categorized!",
    matchInstructions: "Click on an item, then click on its category",
    currentAssignments: "Current Assignments",
    clickToAssign: "Click to assign",
    nowClickCategory: "Now click on a category"
  },
  fr: {
    instructions: "Classez les éléments en cliquant sur un élément à gauche, puis sur sa catégorie à droite.",
    instructionsMobile: "Appuyez sur un élément, puis sur sa catégorie.",
    categories: "Catégories",
    items: "Éléments à classer",
    removeAssignment: "Supprimer l'attribution",
    selectItem: "Sélectionnez d'abord un élément",
    itemSelected: "Élément sélectionné",
    allItemsCategorized: "Tous les éléments ont été classés !",
    matchInstructions: "Cliquez sur un élément, puis sur sa catégorie",
    currentAssignments: "Attributions actuelles",
    clickToAssign: "Cliquez pour assigner",
    nowClickCategory: "Maintenant, clique sur une catégorie"
  },
  de: {
    instructions: "Kategorisiere Gegenstände, indem du auf einen Gegenstand links und dann auf seine Kategorie rechts klickst.",
    instructionsMobile: "Tippe auf einen Gegenstand, dann auf seine Kategorie.",
    categories: "Kategorien",
    items: "Zu kategorisierende Gegenstände",
    removeAssignment: "Zuordnung entfernen",
    selectItem: "Wähle zuerst einen Gegenstand",
    itemSelected: "Gegenstand ausgewählt",
    allItemsCategorized: "Alle Gegenstände wurden kategorisiert!",
    matchInstructions: "Klicke auf einen Gegenstand, dann auf seine Kategorie",
    currentAssignments: "Aktuelle Zuordnungen",
    clickToAssign: "Zum Zuordnen klicken",
    nowClickCategory: "Klicke jetzt auf eine Kategorie"
  },
  lu: {
    instructions: "Kategoriséier Artikelen andeems de op en Artikel lénks an dann op seng Kategorie riets klicks.",
    instructionsMobile: "Tipp op en Artikel, dann op seng Kategorie.",
    categories: "Kategorien",
    items: "Artikelen fir ze kategoriséieren",
    removeAssignment: "Zouuerdnung ewechhuelen",
    selectItem: "Wiel fir d'éischt en Artikel",
    itemSelected: "Artikel ausgewielt",
    allItemsCategorized: "All Artikele goufe kategoriséiert!",
    matchInstructions: "Klick op en Artikel, dann op seng Kategorie",
    currentAssignments: "Aktuell Zouuerdnungen",
    clickToAssign: "Klick fir zouzeuerdnen",
    nowClickCategory: "Klick op eng Kategorie"
  }
};

type CategoryAssignment = {
  optionId: string;
  categoryId: string;
  color: CategoryColors;
};

type CategoryColors = {
  bg: string;
  bgHover: string;
  bgSelected: string;
  border: string;
  borderSelected: string;
  ring: string;
  text: string;
  accent: string;
  classes: string;
  shadow: string;
};

// Color schemes for categories
const getColorOptions = () => [
  {
    bg: 'bg-blue-100/90',
    bgHover: 'hover:bg-blue-100',
    bgSelected: 'bg-blue-200',
    border: 'border-blue-300',
    borderSelected: 'border-blue-500',
    ring: 'ring-blue-400/60',
    text: 'text-blue-900',
    accent: 'bg-blue-500',
    classes: 'bg-blue-100 border-blue-400 text-blue-900',
    shadow: 'shadow-blue-200/50'
  },
  {
    bg: 'bg-emerald-100/90',
    bgHover: 'hover:bg-emerald-100',
    bgSelected: 'bg-emerald-200',
    border: 'border-emerald-300',
    borderSelected: 'border-emerald-500',
    ring: 'ring-emerald-400/60',
    text: 'text-emerald-900',
    accent: 'bg-emerald-500',
    classes: 'bg-emerald-100 border-emerald-400 text-emerald-900',
    shadow: 'shadow-emerald-200/50'
  },
  {
    bg: 'bg-purple-100/90',
    bgHover: 'hover:bg-purple-100',
    bgSelected: 'bg-purple-200',
    border: 'border-purple-300',
    borderSelected: 'border-purple-500',
    ring: 'ring-purple-400/60',
    text: 'text-purple-900',
    accent: 'bg-purple-500',
    classes: 'bg-purple-100 border-purple-400 text-purple-900',
    shadow: 'shadow-purple-200/50'
  },
  {
    bg: 'bg-amber-100/90',
    bgHover: 'hover:bg-amber-100',
    bgSelected: 'bg-amber-200',
    border: 'border-amber-300',
    borderSelected: 'border-amber-500',
    ring: 'ring-amber-400/60',
    text: 'text-amber-900',
    accent: 'bg-amber-500',
    classes: 'bg-amber-100 border-amber-400 text-amber-900',
    shadow: 'shadow-amber-200/50'
  },
  {
    bg: 'bg-rose-100/90',
    bgHover: 'hover:bg-rose-100',
    bgSelected: 'bg-rose-200',
    border: 'border-rose-300',
    borderSelected: 'border-rose-500',
    ring: 'ring-rose-400/60',
    text: 'text-rose-900',
    accent: 'bg-rose-500',
    classes: 'bg-rose-100 border-rose-400 text-rose-900',
    shadow: 'shadow-rose-200/50'
  }
];

interface CategorizeExerciseProps {
  exercise: CategorizeExerciseType;
  isSubmitting: boolean;
  isSubmitted: boolean;
  submissionResult: ExerciseSubmissionResponse | null;
  setIsSubmitting: (isSubmitting: boolean) => void;
  setIsSubmitted: (isSubmitted: boolean) => void;
  setSubmissionResult: (submissionResult: ExerciseSubmissionResponse | null) => void;
}

export const CategorizeExercise = ({
  exercise,
  isSubmitting,
  isSubmitted,
  submissionResult,
  setIsSubmitting,
  setIsSubmitted,
  setSubmissionResult,
}: CategorizeExerciseProps) => {
  const language = useLanguage();
  const translations = languageData[language as Language] || languageData.lu;
  const { submitExercise } = useExercises();
  const colorOptions = getColorOptions();
  
  const [selectedItem, setSelectedItem] = useState<string | null>(null);
  const [assignments, setAssignments] = useState<CategoryAssignment[]>([]);
  const [result, setResult] = useState<Record<string, string>>({});
  const [submissionError, setSubmissionError] = useState<boolean>(false);
  const [showCelebration, setShowCelebration] = useState<boolean>(false);
  const [showIncorrectEffect, setShowIncorrectEffect] = useState<boolean>(false);
  const [recentlyMatched, setRecentlyMatched] = useState<{ optionId: string; categoryId: string } | null>(null);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  // Transform assignments into the format needed for submission
  useEffect(() => {
    const assignmentMap: Record<string, string> = {};
    assignments.forEach(({ optionId, categoryId }) => {
      assignmentMap[optionId] = categoryId;
    });
    setResult(assignmentMap);
  }, [assignments]);

  const selectItem = (optionId: string) => {
    if (isSubmitted) return;
    
    // If this item is already selected, deselect it
    if (selectedItem === optionId) {
      setSelectedItem(null);
      return;
    }
    
    setSelectedItem(optionId);
  };

  const assignToCategory = (categoryId: string) => {
    if (!selectedItem || isSubmitted) return;
    
    // Get the category color based on its index
    const categoryIndex = exercise.categories.findIndex((cat: any) => cat.publicId === categoryId);
    const color = colorOptions[categoryIndex % colorOptions.length];
    
    // Remove any existing assignment for this item
    const filtered = assignments.filter(a => a.optionId !== selectedItem);
    
    // Add the new assignment
    setAssignments([...filtered, { optionId: selectedItem, categoryId, color }]);
    
    // Show match animation
    setRecentlyMatched({ optionId: selectedItem, categoryId });
    setTimeout(() => setRecentlyMatched(null), 1200);
    
    // Clear selection
    setSelectedItem(null);
  };

  const removeAssignment = (assignment: CategoryAssignment) => {
    if (isSubmitted) return;
    setAssignments(prev => prev.filter(a => a !== assignment));
  };

  const getCategoryForOption = (optionId: string) => {
    return assignments.find(a => a.optionId === optionId);
  };

  const getCategoryById = (categoryId: string) => {
    return exercise.categories.find((c: any) => c.publicId === categoryId);
  };

  const getAssignedOptionsForCategory = (categoryId: string) => {
    return assignments.filter(a => a.categoryId === categoryId);
  };

  // Determine if answer is valid for submission (all options must be categorized)
  const allCategorized = assignments.length === exercise.options.length;
  const submissionEnabled = allCategorized && !isSubmitted;
  const progressPercentage = (assignments.length / exercise.options.length) * 100;

  // Handle submission
  const handleSubmission = async () => {
    setIsSubmitting(true);
    setSubmissionError(false);
    
    try {
      const response = await submitExercise(exercise.publicId, result);
      setSubmissionResult(response);
      setIsSubmitted(true);
      
      if (response?.isCorrect) {
        setShowCelebration(true);
      } else {
        setShowIncorrectEffect(true);
      }
    } catch (error) {
      console.error('Error submitting answer:', error);
      setSubmissionError(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="relative">
      {/* Exercise Content */}
      <AnimatePresence mode="wait">
        {isSubmitting ? (
          <SubmissionAnimation key="submission-animation" isSubmitting={isSubmitting} />
        ) : (
          <motion.div
            key="exercise-content"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
          >
            {/* Instructions */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
              className="mb-4 p-3 bg-blue-50/50 border border-blue-100 rounded-lg"
            >
              <div className="flex items-center gap-2">
                <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
                <span className="text-sm text-blue-700 font-medium">
                  <span className="hidden sm:inline">{translations.instructions}</span>
                  <span className="sm:hidden">{translations.instructionsMobile}</span>
                </span>
              </div>
            </motion.div>

            {/* Two columns layout */}
            <div className="flex flex-col sm:grid sm:grid-cols-2 gap-8 relative">
              {/* Visual connector between columns on md+ screens */}
              <div className="hidden sm:block absolute left-1/2 top-0 bottom-0 w-px bg-gradient-to-b from-transparent via-gray-300 to-transparent -translate-x-1/2" />

              {/* Items Column (Left) */}
              <div className="space-y-4">
                <motion.div
                  className="flex items-center gap-3 mb-4"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4 }}
                >
                  <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                  </div>
                  <div className="flex flex-col md:flex-row md:items-center gap-2">
                    <h4 className="font-bold text-lg text-gray-800">{translations.items}</h4>
                    {selectedItem && (
                      <motion.div
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="px-2 py-1 bg-blue-100 text-blue-700 text-xs rounded-full font-medium"
                      >
                        {translations.itemSelected}
                      </motion.div>
                    )}
                  </div>
                </motion.div>

                <div className="space-y-3">
                  {exercise.options.map((option: any, index: number) => {
                    const assignment = getCategoryForOption(option.publicId);
                    const isSelected = selectedItem === option.publicId;
                    const isRecentlyMatched = recentlyMatched?.optionId === option.publicId;
                    const isHovered = hoveredItem === option.publicId;
                    const isSelectable = !isSubmitted && !assignment;

                    return (
                      <motion.div
                        key={option.publicId}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{
                          opacity: 1,
                          y: 0,
                          scale: isRecentlyMatched ? [1, 1.05, 1] : 1,
                        }}
                        transition={{
                          duration: 0.1,
                          ease: "easeOut"
                        }}
                        onHoverStart={() => setHoveredItem(option.publicId)}
                        onHoverEnd={() => setHoveredItem(null)}
                        onClick={() => (isSelectable || isSelected) && selectItem(option.publicId)}
                        className={`relative p-4 rounded-xl border-2 transition-all duration-200 shadow-sm group
                          ${isSelectable || isSelected ? 'cursor-pointer hover:shadow-md hover:border-blue-400 hover:bg-blue-50/30' : ''}
                          ${isSelected
                            ? 'bg-blue-200 border-blue-500 ring-2 ring-blue-400/60 shadow-md'
                            : assignment
                              ? `${assignment.color.bg} ${assignment.color.border} shadow-sm`
                              : 'border-gray-200 bg-white hover:border-gray-300'
                          }`}
                        whileHover={(isSelectable || isSelected) ? {
                          scale: 1.02,
                          y: -3,
                          transition: { duration: 0.1, ease: "easeOut" }
                        } : {}}
                        whileTap={(isSelectable || isSelected) ? {
                          scale: 0.98
                        } : {}}
                      >
                        {/* Color accent bar */}
                        {assignment && (
                          <div className={`absolute left-0 top-0 bottom-0 w-2 rounded-l-xl ${assignment.color.accent}`} />
                        )}

                        {/* Selected indicator */}
                        {isSelected && (
                          <motion.div
                            initial={{ opacity: 0, scale: 0 }}
                            animate={{ opacity: 1, scale: 1 }}
                            className="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center shadow-md"
                          >
                            <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </motion.div>
                        )}

                        {/* Hover glow effect */}
                        {isHovered && (isSelectable || isSelected) && (
                          <motion.div
                            className="absolute inset-0 bg-blue-400/10 rounded-xl border-2 border-blue-400/30"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                          />
                        )}

                        {/* Match sparkle effect */}
                        {isRecentlyMatched && (
                          <motion.div
                            className="absolute inset-0 pointer-events-none"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                          >
                            {[...Array(6)].map((_, i) => (
                              <motion.div
                                key={i}
                                className="absolute w-1.5 h-1.5 bg-yellow-400 rounded-full"
                                style={{
                                  left: `${15 + i * 15}%`,
                                  top: `${20 + (i % 3) * 20}%`,
                                }}
                                initial={{ scale: 0, rotate: 0 }}
                                animate={{
                                  scale: [0, 1.2, 0],
                                  rotate: 360,
                                  opacity: [0, 1, 0]
                                }}
                                transition={{
                                  duration: 1,
                                  delay: i * 0.1,
                                  ease: "easeOut"
                                }}
                              />
                            ))}
                          </motion.div>
                        )}

                        <div className={`font-medium text-base ${isSelected ? 'text-blue-900' : 
                          assignment ? assignment.color.text : 'text-gray-800'
                        }`}>
                          <LatexRenderer content={option.text} />
                        </div>

                        {/* Assigned category label */}
                        {assignment && (
                          <div className="mt-2 flex items-center gap-2 text-xs">
                            <span className={`${assignment.color.text} opacity-75`}>
                              → {getCategoryById(assignment.categoryId)?.text}
                            </span>
                          </div>
                        )}
                      </motion.div>
                    );
                  })}
                </div>
              </div>

              {/* Categories Column (Right) */}
              <div className="space-y-4">
                <motion.div
                  className="flex items-center gap-3 mb-4"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: 0.2 }}
                >
                  <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                    <svg className="w-4 h-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                  </div>
                  <div className="flex flex-col md:flex-row md:items-center gap-2">
                    <h4 className="font-bold text-lg text-gray-800">{translations.categories}</h4>
                    <div className="text-sm text-gray-500">
                      {selectedItem ? translations.clickToAssign : translations.selectItem}
                    </div>
                  </div>
                </motion.div>

                <div className="space-y-3">
                  {exercise.categories.map((category: any, index: number) => {
                    const color = colorOptions[index % colorOptions.length];
                    const assignedCount = getAssignedOptionsForCategory(category.publicId).length;
                    const isRecentlyMatched = recentlyMatched?.categoryId === category.publicId;
                    const isHovered = hoveredItem === category.publicId;
                    const isSelectable = !isSubmitted && selectedItem;

                    return (
                      <motion.div
                        key={category.publicId}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{
                          opacity: 1,
                          y: 0,
                          scale: isRecentlyMatched ? [1, 1.05, 1] : 1,
                        }}
                        transition={{
                          duration: 0.1,
                          ease: "easeOut"
                        }}
                        onHoverStart={() => setHoveredItem(category.publicId)}
                        onHoverEnd={() => setHoveredItem(null)}
                        onClick={() => isSelectable && assignToCategory(category.publicId)}
                        className={`relative p-4 rounded-xl border-2 transition-all duration-200 shadow-sm group
                          ${isSelectable ? 'cursor-pointer hover:shadow-md hover:border-green-400 hover:bg-green-50/30' : ''}
                          ${!isSelectable && !selectedItem ? 'opacity-60' : ''}
                          ${color.bg} ${color.border} shadow-sm
                        `}
                        whileHover={isSelectable ? {
                          scale: 1.02,
                          y: -3,
                          transition: { duration: 0.1, ease: "easeOut" }
                        } : {}}
                        whileTap={isSelectable ? {
                          scale: 0.98
                        } : {}}
                      >
                        {/* Color accent bar */}
                        <div className={`absolute left-0 top-0 bottom-0 w-2 rounded-l-xl ${color.accent}`} />

                        {/* Assignment count badge */}
                        {assignedCount > 0 && (
                          <motion.div
                            initial={{ opacity: 0, scale: 0 }}
                            animate={{ opacity: 1, scale: 1 }}
                            className={`absolute -top-2 -right-2 w-6 h-6 ${color.accent} text-white rounded-full flex items-center justify-center shadow-md text-xs font-bold`}
                          >
                            {assignedCount}
                          </motion.div>
                        )}

                        {/* Hover glow effect */}
                        {isHovered && isSelectable && (
                          <motion.div
                            className="absolute inset-0 bg-green-400/10 rounded-xl border-2 border-green-400/30"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                          />
                        )}

                        {/* Match sparkle effect */}
                        {isRecentlyMatched && (
                          <motion.div
                            className="absolute inset-0 pointer-events-none"
                            initial={{ opacity: 0 }}
                            animate={{ opacity: 1 }}
                            exit={{ opacity: 0 }}
                          >
                            {[...Array(6)].map((_, i) => (
                              <motion.div
                                key={i}
                                className="absolute w-1.5 h-1.5 bg-yellow-400 rounded-full"
                                style={{
                                  left: `${15 + i * 15}%`,
                                  top: `${20 + (i % 3) * 20}%`,
                                }}
                                initial={{ scale: 0, rotate: 0 }}
                                animate={{
                                  scale: [0, 1.2, 0],
                                  rotate: 360,
                                  opacity: [0, 1, 0]
                                }}
                                transition={{
                                  duration: 1,
                                  delay: i * 0.1,
                                  ease: "easeOut"
                                }}
                              />
                            ))}
                          </motion.div>
                        )}

                        <div className={`font-medium text-base ${color.text}`}>
                          <LatexRenderer content={category.text} />
                        </div>
                      </motion.div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Instructions */}
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="mt-6 text-sm text-gray-600 bg-blue-50/50 p-3 rounded-lg border border-blue-100"
            >
              {selectedItem ? (
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  <span>{translations.nowClickCategory}</span>
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>{translations.matchInstructions}</span>
                </div>
              )}
            </motion.div>

            {/* Current Assignments Section */}
            <AnimatePresence>
              {!isSubmitted && assignments.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 15 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -15 }}
                  transition={{ duration: 0.3, ease: "easeOut" }}
                  className="mt-6 p-4 bg-gradient-to-r from-gray-50 to-slate-50 border border-gray-200 rounded-lg shadow-sm"
                >
                  <motion.div
                    className="flex items-center gap-2 mb-3"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1, duration: 0.3 }}
                  >
                    <motion.div
                      className="w-2 h-2 bg-gray-500 rounded-full"
                      animate={{ scale: [1, 1.2, 1] }}
                      transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                    />
                    <h5 className="text-base font-semibold text-gray-800">
                      {translations.currentAssignments} ({assignments.length})
                    </h5>
                  </motion.div>

                  <div className="space-y-2">
                    <AnimatePresence>
                      {assignments.map((assignment, i) => {
                        const option = exercise.options.find((o: any) => o.publicId === assignment.optionId);
                        const category = getCategoryById(assignment.categoryId);
                        
                        return (
                          <motion.div
                            key={`${assignment.optionId}-${assignment.categoryId}`}
                            initial={{ opacity: 0, scale: 0.95, x: -15 }}
                            animate={{ opacity: 1, scale: 1, x: 0 }}
                            exit={{ opacity: 0, scale: 0.95, x: -15 }}
                            transition={{ duration: 0.3, ease: "easeOut" }}
                            className={`group flex items-center justify-between gap-3 p-3 rounded-lg ${assignment.color.classes} border-2 border-transparent hover:border-white/50 transition-all duration-200 shadow-sm`}
                          >
                            <div className="flex items-center gap-3 flex-grow">
                              <motion.div
                                className="flex-shrink-0 w-6 h-6 bg-white/30 rounded-full flex items-center justify-center text-xs font-bold"
                                whileHover={{ scale: 1.1, rotate: 5 }}
                                transition={{ duration: 0.2 }}
                              >
                                {i + 1}
                              </motion.div>
                              <div className="flex items-center gap-2 text-sm font-medium">
                                <span className="bg-white/40 px-2 py-1 rounded text-xs font-semibold">
                                  <LatexRenderer content={option?.text} />
                                </span>
                                <motion.div
                                  className="text-lg"
                                  animate={{ x: [0, 3, 0] }}
                                  transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
                                >
                                  →
                                </motion.div>
                                <span className="bg-white/40 px-2 py-1 rounded text-xs font-semibold">
                                  <LatexRenderer content={category?.text} />
                                </span>
                              </div>
                            </div>

                            <motion.button
                              onClick={() => removeAssignment(assignment)}
                              className="flex-shrink-0 w-8 h-8 bg-red-100 hover:bg-red-200 text-red-600 rounded-full flex items-center justify-center transition-colors duration-200 opacity-70 group-hover:opacity-100"
                              whileHover={{ scale: 1.1, rotate: 90 }}
                              whileTap={{ scale: 0.9 }}
                              transition={{ duration: 0.2 }}
                              title={translations.removeAssignment}
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </motion.button>
                          </motion.div>
                        );
                      })}
                    </AnimatePresence>
                  </div>

                  {/* Progress indicator */}
                  <motion.div
                    className="mt-3 pt-3 border-t border-gray-200"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.3 }}
                  >
                    <div className="flex items-center justify-center">
                      <motion.div
                        className="flex gap-1"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ delay: 0.4 }}
                      >
                        {Array.from({ length: exercise.options.length }).map((_, index) => (
                          <motion.div
                            key={index}
                            className={`w-2 h-2 rounded-full ${index < assignments.length ? 'bg-gray-500' : 'bg-gray-200'}`}
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            transition={{ delay: 0.5 + index * 0.05, duration: 0.2 }}
                          />
                        ))}
                      </motion.div>
                    </div>
                  </motion.div>
                </motion.div>
              )}
            </AnimatePresence>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Submission Area */}
      {submissionError && <SubmissionError language={language} />}
      <div className="mt-8">
        <SubmissionButton
          isDisabled={!submissionEnabled}
          onSubmit={handleSubmission}
          isSubmitting={isSubmitting}
        />
      </div>

      {/* Solution/Feedback Area */}
      <AnimatePresence>
        {isSubmitted && !isSubmitting && submissionResult && (
          <motion.div
            key="solution-feedback"
            initial={{ opacity: 0, y: 15 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0 }}
            transition={{ delay: 0.1 }}
            className="mt-6"
          >
            <ExerciseSolution
              correctlyAnswered={submissionResult.isCorrect}
              correctAnswer={submissionResult.correctAnswer as string[]}
              videoUrl={submissionResult.videoUrl || undefined}
              solutionSteps={submissionResult.solutionSteps?.filter(step => step !== null) || undefined}
              language={language}
              recentlySubmitted={submissionResult.recentlySubmitted}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Celebration Effect */}
      <CelebrationEffect
        trigger={showCelebration}
        onComplete={() => setShowCelebration(false)}
      />

      {/* Incorrect Answer Effect */}
      <IncorrectAnswerEffect
        trigger={showIncorrectEffect}
        onComplete={() => setShowIncorrectEffect(false)}
      />
    </div>
  );
};

export default CategorizeExercise;